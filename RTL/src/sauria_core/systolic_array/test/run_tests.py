#!/usr/bin/env python3
"""
Test runner script for zero_det_neg module

This script provides a convenient interface for running the cocotb tests
with various configurations and generating reports.
"""

import argparse
import subprocess
import sys
import os
import time
from pathlib import Path

class TestRunner:
    """Test runner for zero_det_neg module"""
    
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.results = []
        
    def run_command(self, cmd, description=""):
        """Run a command and capture results"""
        print(f"\n{'='*60}")
        print(f"Running: {description}")
        print(f"Command: {' '.join(cmd)}")
        print(f"{'='*60}")
        
        start_time = time.time()
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.script_dir,
                capture_output=True, 
                text=True, 
                check=True
            )
            duration = time.time() - start_time
            
            print("STDOUT:")
            print(result.stdout)
            if result.stderr:
                print("STDERR:")
                print(result.stderr)
                
            self.results.append({
                'description': description,
                'success': True,
                'duration': duration,
                'stdout': result.stdout,
                'stderr': result.stderr
            })
            
            print(f"✓ SUCCESS ({duration:.2f}s)")
            return True
            
        except subprocess.CalledProcessError as e:
            duration = time.time() - start_time
            
            print("STDOUT:")
            print(e.stdout)
            print("STDERR:")
            print(e.stderr)
            
            self.results.append({
                'description': description,
                'success': False,
                'duration': duration,
                'stdout': e.stdout,
                'stderr': e.stderr,
                'returncode': e.returncode
            })
            
            print(f"✗ FAILED ({duration:.2f}s) - Return code: {e.returncode}")
            return False
            
    def run_basic_tests(self, simulator="verilator"):
        """Run basic test suite"""
        cmd = ["make", "test", f"SIM={simulator}"]
        return self.run_command(cmd, f"Basic tests with {simulator}")
        
    def run_parameter_sweep(self, simulator="verilator"):
        """Run tests with different parameter configurations"""
        configs = [
            {"IA_W": 8, "IB_W": 8, "TH_W": 2},
            {"IA_W": 16, "IB_W": 16, "TH_W": 2},
            {"IA_W": 32, "IB_W": 32, "TH_W": 3},
            {"IA_W": 16, "IB_W": 8, "TH_W": 2},
            {"IA_W": 8, "IB_W": 16, "TH_W": 3},
        ]
        
        success_count = 0
        for config in configs:
            params = " ".join([f"{k}={v}" for k, v in config.items()])
            cmd = ["make", "test", f"SIM={simulator}"] + [f"{k}={v}" for k, v in config.items()]
            description = f"Parameter sweep: {params} with {simulator}"
            
            if self.run_command(cmd, description):
                success_count += 1
                
        return success_count == len(configs)
        
    def run_all_simulators(self):
        """Run tests with all available simulators"""
        simulators = ["verilator"]
        
        # Check for other simulators
        if subprocess.run(["which", "vcs"], capture_output=True).returncode == 0:
            simulators.append("vcs")
        if subprocess.run(["which", "vsim"], capture_output=True).returncode == 0:
            simulators.append("questa")
            
        success_count = 0
        for sim in simulators:
            if self.run_basic_tests(sim):
                success_count += 1
                
        return success_count == len(simulators)
        
    def clean_artifacts(self):
        """Clean build artifacts"""
        cmd = ["make", "clean"]
        return self.run_command(cmd, "Cleaning build artifacts")
        
    def generate_report(self):
        """Generate test report"""
        print(f"\n{'='*80}")
        print("TEST REPORT SUMMARY")
        print(f"{'='*80}")
        
        total_tests = len(self.results)
        successful_tests = sum(1 for r in self.results if r['success'])
        failed_tests = total_tests - successful_tests
        total_duration = sum(r['duration'] for r in self.results)
        
        print(f"Total tests run: {total_tests}")
        print(f"Successful: {successful_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Total duration: {total_duration:.2f}s")
        print(f"Success rate: {(successful_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\nFAILED TESTS:")
            for result in self.results:
                if not result['success']:
                    print(f"  ✗ {result['description']}")
                    if 'returncode' in result:
                        print(f"    Return code: {result['returncode']}")
                        
        print(f"\nSUCCESSFUL TESTS:")
        for result in self.results:
            if result['success']:
                print(f"  ✓ {result['description']} ({result['duration']:.2f}s)")
                
        return failed_tests == 0


def main():
    parser = argparse.ArgumentParser(description="Test runner for zero_det_neg module")
    parser.add_argument(
        "--mode", 
        choices=["basic", "sweep", "all-sims", "comprehensive"], 
        default="basic",
        help="Test mode to run"
    )
    parser.add_argument(
        "--simulator", 
        default="verilator",
        help="Simulator to use (default: verilator)"
    )
    parser.add_argument(
        "--clean", 
        action="store_true",
        help="Clean artifacts before running tests"
    )
    parser.add_argument(
        "--no-report", 
        action="store_true",
        help="Skip generating final report"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    print("Zero Detection and Negligence Logic Test Runner")
    print("=" * 50)
    
    # Clean if requested
    if args.clean:
        runner.clean_artifacts()
        
    # Run tests based on mode
    overall_success = True
    
    if args.mode == "basic":
        overall_success = runner.run_basic_tests(args.simulator)
        
    elif args.mode == "sweep":
        overall_success = runner.run_parameter_sweep(args.simulator)
        
    elif args.mode == "all-sims":
        overall_success = runner.run_all_simulators()
        
    elif args.mode == "comprehensive":
        # Run everything
        overall_success &= runner.run_basic_tests(args.simulator)
        overall_success &= runner.run_parameter_sweep(args.simulator)
        if args.simulator == "verilator":  # Only try other sims if using default
            overall_success &= runner.run_all_simulators()
            
    # Generate report
    if not args.no_report:
        report_success = runner.generate_report()
        overall_success &= report_success
        
    # Exit with appropriate code
    sys.exit(0 if overall_success else 1)


if __name__ == "__main__":
    main()
