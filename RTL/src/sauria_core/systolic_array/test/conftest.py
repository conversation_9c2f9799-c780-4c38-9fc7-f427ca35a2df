"""
Pytest configuration for zero_det_neg tests

This file provides pytest fixtures and configuration for the cocotb tests.
"""

import pytest
import os
import sys

def pytest_configure(config):
    """Configure pytest for cocotb testing"""
    # Add current directory to Python path for imports
    sys.path.insert(0, os.path.dirname(__file__))

def pytest_addoption(parser):
    """Add custom command line options"""
    parser.addoption(
        "--ia-w", 
        action="store", 
        default=16, 
        type=int,
        help="Input A width parameter"
    )
    parser.addoption(
        "--ib-w", 
        action="store", 
        default=16, 
        type=int,
        help="Input B width parameter"
    )
    parser.addoption(
        "--th-w", 
        action="store", 
        default=2, 
        type=int,
        help="Threshold width parameter"
    )
    parser.addoption(
        "--simulator", 
        action="store", 
        default="verilator",
        help="Simulator to use (verilator, vcs, questa)"
    )

@pytest.fixture(scope="session")
def test_parameters(request):
    """Provide test parameters as a fixture"""
    return {
        'ia_w': request.config.getoption("--ia-w"),
        'ib_w': request.config.getoption("--ib-w"),
        'th_w': request.config.getoption("--th-w"),
        'simulator': request.config.getoption("--simulator")
    }

@pytest.fixture(scope="session")
def test_vectors():
    """Provide common test vectors for reuse across tests"""
    return {
        'zero_values': [0],
        'small_positive': [1, 2, 3, 4, 7, 8, 15, 16],
        'small_negative': [0xFFFF, 0xFFFE, 0xFFFD, 0xFFFC, 0xFFF9, 0xFFF8, 0xFFF1, 0xFFF0],
        'large_positive': [100, 1000, 0x7FFF],
        'large_negative': [0x8000, 0x8001, 0x9000],
        'boundary_values': [0x7FFF, 0x8000, 0xFFFF, 0x0001]
    }

# Pytest markers for test categorization
pytest_plugins = []

def pytest_collection_modifyitems(config, items):
    """Add markers to tests based on their names"""
    for item in items:
        # Add markers based on test function names
        if "exact_zero" in item.name:
            item.add_marker(pytest.mark.exact_zero)
        elif "negligence" in item.name:
            item.add_marker(pytest.mark.negligence)
        elif "boundary" in item.name:
            item.add_marker(pytest.mark.boundary)
        elif "random" in item.name:
            item.add_marker(pytest.mark.random)
        elif "comprehensive" in item.name:
            item.add_marker(pytest.mark.comprehensive)
            
        # Add slow marker for comprehensive tests
        if "comprehensive" in item.name or "random" in item.name:
            item.add_marker(pytest.mark.slow)
