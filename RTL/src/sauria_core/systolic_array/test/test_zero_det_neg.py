#!/usr/bin/env python3
"""
Comprehensive cocotb tests for zero_det_neg.sv module

This module tests the zero detection and negligence logic for approximate computing
in the SAURIA systolic array neural network accelerator.

Author: Generated test suite
License: Apache-2.0 WITH SHL-2.1
"""

import cocotb
from cocotb.triggers import Timer
from cocotb.clock import Clock
from cocotb.result import TestFailure
import random
import pytest
import numpy as np
from typing import Tuple, List


class ZeroDetNegTester:
    """Test harness for zero_det_neg module"""
    
    def __init__(self, dut, ia_w: int = 16, ib_w: int = 16, th_w: int = 2):
        self.dut = dut
        self.ia_w = ia_w
        self.ib_w = ib_w
        self.th_w = th_w
        self.max_threshold = (2**th_w) - 1
        
    async def reset_inputs(self):
        """Reset all inputs to known state"""
        self.dut.i_a.value = 0
        self.dut.i_b.value = 0
        self.dut.i_thres.value = 0
        await Timer(1, units='ns')
        
    def to_signed(self, value: int, width: int) -> int:
        """Convert unsigned value to signed interpretation"""
        if value >= (1 << (width - 1)):
            return value - (1 << width)
        return value
        
    def from_signed(self, value: int, width: int) -> int:
        """Convert signed value to unsigned representation"""
        if value < 0:
            return value + (1 << width)
        return value
        
    def is_negligible_signed(self, value: int, width: int, threshold: int) -> bool:
        """
        Check if a signed integer value is negligible based on threshold.

        The SystemVerilog logic works as follows:
        1. Create threshold decoder: thres_dec[b] = 1 if b < threshold
        2. For masked bits (where thres_dec[b] = 1): set to 0 for pos, 1 for neg
        3. For unmasked bits: copy actual input bits
        4. Check if result is all 0s (positive) or all 1s (negative)

        This means negligence is detected when the UNMASKED bits follow the pattern.
        """
        if threshold == 0:
            return False

        # Calculate dec_bits (maximum bits that can be masked)
        dec_bits = min(self.max_threshold, width - 1)

        # Build the comparison inputs as the SystemVerilog does
        neg_inputs_p = 0
        neg_inputs_n = 0

        # Process bits 1 to width-1 (bit 0 is excluded)
        for bit_pos in range(1, width):
            if bit_pos < dec_bits and bit_pos < threshold:
                # Masked bit: set to 0 for positive, 1 for negative
                neg_inputs_p |= (0 << bit_pos)  # Already 0, but explicit
                neg_inputs_n |= (1 << bit_pos)
            else:
                # Unmasked bit: copy from input
                bit_val = (value >> bit_pos) & 1
                neg_inputs_p |= (bit_val << bit_pos)
                neg_inputs_n |= (bit_val << bit_pos)

        # Create mask for the bits we're checking (bits 1 to width-1)
        check_mask = ((1 << width) - 1) & ~1  # All bits except bit 0

        # Check if all checked bits are 0 (positive case) or all 1 (negative case)
        all_zeros = (neg_inputs_p & check_mask) == 0
        all_ones = (neg_inputs_n & check_mask) == check_mask

        return all_zeros or all_ones
            
    def expected_output(self, a: int, b: int, threshold: int) -> bool:
        """Calculate expected output based on module logic"""
        # Exact zero detection
        exact_zero = (a == 0) or (b == 0)
        
        # Negligence detection (only if NEGLIGENCE is defined)
        negligence = False
        if threshold > 0:  # Negligence only active when threshold > 0
            neg_a = self.is_negligible_signed(a, self.ia_w, threshold)
            neg_b = self.is_negligible_signed(b, self.ib_w, threshold)
            negligence = neg_a and neg_b
            
        return exact_zero or negligence
        
    async def test_single_case(self, a: int, b: int, threshold: int) -> bool:
        """Test a single input combination"""
        # Set inputs
        self.dut.i_a.value = a
        self.dut.i_b.value = b
        self.dut.i_thres.value = threshold
        
        # Wait for combinational logic to settle
        await Timer(1, units='ns')
        
        # Get actual output
        actual = bool(self.dut.o_zero_det.value)
        
        # Calculate expected output
        expected = self.expected_output(a, b, threshold)
        
        return actual == expected


@cocotb.test()
async def test_exact_zero_detection(dut):
    """Test exact zero detection functionality"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()
    
    test_cases = [
        # (a, b, threshold, description)
        (0, 0, 0, "Both zero"),
        (0, 1, 0, "A zero, B non-zero"),
        (1, 0, 0, "A non-zero, B zero"),
        (0, 0xFFFF, 0, "A zero, B max"),
        (0x8000, 0, 0, "A negative, B zero"),
        (0, 0x8000, 0, "A zero, B negative"),
    ]
    
    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        assert success, f"Failed exact zero test: {desc} (a={a:04x}, b={b:04x}, th={threshold})"
        
    dut._log.info("✓ Exact zero detection tests passed")


@cocotb.test()
async def test_no_false_positives_without_negligence(dut):
    """Test that non-zero values don't trigger detection when threshold=0"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()
    
    # Test various non-zero combinations with threshold=0
    test_values = [1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 
                   0x7FFF, 0x8001, 0xFFFF, 0x8000]
    
    for a in test_values:
        for b in test_values:
            success = await tester.test_single_case(a, b, 0)
            assert success, f"False positive with threshold=0: a={a:04x}, b={b:04x}"
            
    dut._log.info("✓ No false positives without negligence")


@cocotb.test()
async def test_negligence_threshold_1(dut):
    """Test negligence detection with threshold=1"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()
    
    # With threshold=1, bit 1 should be masked for negligence detection
    test_cases = [
        # Small positive values (should be negligible)
        (1, 1, 1, "Both small positive"),
        (1, 3, 1, "Mixed small positive"),
        
        # Small negative values (should be negligible) 
        (0xFFFF, 0xFFFD, 1, "Both small negative"),
        (0xFFFF, 0xFFFF, 1, "Both -1"),
        
        # Mixed small values
        (1, 0xFFFF, 1, "Small positive and small negative"),
        
        # Values that should NOT be negligible
        (4, 4, 1, "Both too large for threshold=1"),
        (1, 4, 1, "One small, one large"),
    ]
    
    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        expected = tester.expected_output(a, b, threshold)
        actual = bool(dut.o_zero_det.value)
        assert success, f"Failed negligence test: {desc} (a={a:04x}, b={b:04x}, expected={expected}, actual={actual})"
        
    dut._log.info("✓ Negligence threshold=1 tests passed")


@cocotb.test()
async def test_negligence_threshold_2(dut):
    """Test negligence detection with threshold=2"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()
    
    # With threshold=2, bits 1 and 2 should be masked
    test_cases = [
        # Values negligible with threshold=2
        (1, 1, 2, "Small values"),
        (3, 3, 2, "Slightly larger small values"),
        (0xFFFF, 0xFFFD, 2, "Small negative values"),
        
        # Values NOT negligible with threshold=2
        (4, 4, 2, "Too large for threshold=2"),
        (8, 1, 2, "One large, one small"),
    ]
    
    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        assert success, f"Failed negligence test: {desc} (a={a:04x}, b={b:04x}, th={threshold})"
        
    dut._log.info("✓ Negligence threshold=2 tests passed")


@cocotb.test()
async def test_negligence_threshold_3(dut):
    """Test negligence detection with threshold=3 (maximum for TH_W=2)"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()
    
    # With threshold=3, bits 1, 2, and 3 should be masked
    test_cases = [
        (1, 1, 3, "Small positive values"),
        (7, 7, 3, "Larger small values"),
        (0xFFFF, 0xFFF9, 3, "Small negative values"),
        (8, 8, 3, "Too large for threshold=3"),
    ]
    
    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        assert success, f"Failed negligence test: {desc} (a={a:04x}, b={b:04x}, th={threshold})"
        
    dut._log.info("✓ Negligence threshold=3 tests passed")


@cocotb.test()
async def test_boundary_conditions(dut):
    """Test boundary conditions and edge cases"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()

    # Test maximum and minimum values
    test_cases = [
        # Maximum positive value
        (0x7FFF, 0x7FFF, 0, "Max positive, no negligence"),
        (0x7FFF, 0x7FFF, 1, "Max positive, threshold=1"),

        # Maximum negative value (most negative)
        (0x8000, 0x8000, 0, "Most negative, no negligence"),
        (0x8000, 0x8000, 1, "Most negative, threshold=1"),

        # Mixed maximum values
        (0x7FFF, 0x8000, 0, "Max positive and most negative"),
        (0x7FFF, 0x8000, 1, "Max positive and most negative, threshold=1"),

        # Values at threshold boundaries
        (2, 2, 1, "At threshold=1 boundary"),
        (4, 4, 2, "At threshold=2 boundary"),
        (8, 8, 3, "At threshold=3 boundary"),
    ]

    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        assert success, f"Failed boundary test: {desc} (a={a:04x}, b={b:04x}, th={threshold})"

    dut._log.info("✓ Boundary condition tests passed")


@cocotb.test()
async def test_asymmetric_negligence(dut):
    """Test cases where only one operand is negligible"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()

    # These should NOT trigger negligence (both operands must be negligible)
    test_cases = [
        (1, 100, 1, "A negligible, B large"),
        (100, 1, 1, "A large, B negligible"),
        (1, 0x7FFF, 2, "A small, B max positive"),
        (0x8000, 1, 2, "A most negative, B small"),
        (0xFFFF, 100, 1, "A small negative, B large positive"),
    ]

    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        expected = tester.expected_output(a, b, threshold)
        # These should all be False (no detection) since only one operand is negligible
        assert not expected, f"Expected no detection for asymmetric case: {desc}"
        assert success, f"Failed asymmetric test: {desc} (a={a:04x}, b={b:04x}, th={threshold})"

    dut._log.info("✓ Asymmetric negligence tests passed")


@cocotb.test()
async def test_signed_negative_patterns(dut):
    """Test signed negative number negligence patterns"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()

    # Test negative numbers that should be negligible
    # For 16-bit signed: -1 = 0xFFFF, -2 = 0xFFFE, etc.
    test_cases = [
        # Small negative values with different thresholds
        (0xFFFF, 0xFFFF, 1, "-1, -1 with threshold=1"),  # Should be negligible
        (0xFFFE, 0xFFFE, 1, "-2, -2 with threshold=1"),  # Should NOT be negligible (bit 1 is 0)
        (0xFFFD, 0xFFFD, 2, "-3, -3 with threshold=2"),  # Should be negligible
        (0xFFFB, 0xFFFB, 2, "-5, -5 with threshold=2"),  # Should NOT be negligible (bit 2 is 0)

        # Mixed positive and negative small values
        (1, 0xFFFF, 1, "1, -1 with threshold=1"),        # Should be negligible
        (3, 0xFFFD, 2, "3, -3 with threshold=2"),        # Should be negligible
    ]

    for a, b, threshold, desc in test_cases:
        success = await tester.test_single_case(a, b, threshold)
        expected = tester.expected_output(a, b, threshold)
        actual_output = bool(dut.o_zero_det.value)

        assert success, f"Failed signed negative test: {desc} (a={a:04x}, b={b:04x}, expected={expected}, actual={actual_output})"

    dut._log.info("✓ Signed negative pattern tests passed")


@cocotb.test()
async def test_random_comprehensive(dut):
    """Comprehensive random testing"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()

    # Set random seed for reproducibility
    random.seed(42)

    num_tests = 1000
    failures = []

    for i in range(num_tests):
        # Generate random inputs
        a = random.randint(0, (1 << tester.ia_w) - 1)
        b = random.randint(0, (1 << tester.ib_w) - 1)
        threshold = random.randint(0, tester.max_threshold)

        success = await tester.test_single_case(a, b, threshold)
        if not success:
            expected = tester.expected_output(a, b, threshold)
            actual = bool(dut.o_zero_det.value)
            failures.append(f"Test {i}: a={a:04x}, b={b:04x}, th={threshold}, expected={expected}, actual={actual}")

        # Limit failure reporting to avoid overwhelming output
        if len(failures) >= 10:
            break

    assert len(failures) == 0, f"Random test failures:\n" + "\n".join(failures)
    dut._log.info(f"✓ Random comprehensive testing passed ({num_tests} tests)")


@cocotb.test()
async def test_threshold_progression(dut):
    """Test how the same values behave with increasing thresholds"""
    tester = ZeroDetNegTester(dut)
    await tester.reset_inputs()

    # Test specific value pairs across all thresholds
    test_pairs = [
        (1, 1),      # Should become negligible at threshold=1
        (3, 3),      # Should become negligible at threshold=2
        (7, 7),      # Should become negligible at threshold=3
        (15, 15),    # Should never be negligible (requires threshold=4)
        (0xFFFF, 0xFFFF),  # -1, -1: should be negligible at threshold=1
        (0xFFFD, 0xFFFD),  # -3, -3: should be negligible at threshold=2
    ]

    for a, b in test_pairs:
        results = []
        for threshold in range(4):  # Test thresholds 0, 1, 2, 3
            success = await tester.test_single_case(a, b, threshold)
            expected = tester.expected_output(a, b, threshold)
            results.append(expected)
            assert success, f"Threshold progression test failed: a={a:04x}, b={b:04x}, th={threshold}"

        # Log the progression for debugging
        dut._log.info(f"Threshold progression for a={a:04x}, b={b:04x}: {results}")

    dut._log.info("✓ Threshold progression tests passed")
