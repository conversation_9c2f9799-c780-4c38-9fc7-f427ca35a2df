# Makefile for cocotb tests of zero_det_neg module
# Compatible with SAURIA project structure and Verilator

# Default simulator
SIM ?= verilator

# Project paths
SAURIA_ROOT := $(shell pwd)/../../../..
RTL_ROOT := $(SAURIA_ROOT)/RTL
PULP_DIR := $(SAURIA_ROOT)/pulp_platform

# Module under test
TOPLEVEL := zero_det_neg
MODULE := test_zero_det_neg

# Source files
VERILOG_SOURCES := $(PWD)/../zero_det_neg.sv

# Include directories (following SAURIA project structure)
COMPILE_ARGS += +incdir+$(PULP_DIR)/axi/include
COMPILE_ARGS += +incdir+$(PULP_DIR)/common_cells/include

# Verilator specific flags (based on SAURIA's verilator setup)
ifeq ($(SIM), verilator)
    COMPILE_ARGS += --trace
    COMPILE_ARGS += --trace-max-array 256
    COMPILE_ARGS += --trace-max-width 256
    COMPILE_ARGS += -Wno-lint
    COMPILE_ARGS += -Wno-style
    COMPILE_ARGS += -Wno-STMTDLY
    COMPILE_ARGS += -Wno-fatal
    COMPILE_ARGS += --unroll-count 256
    COMPILE_ARGS += --assert
    
    # Define macros to match the module configuration
    COMPILE_ARGS += +define+NEGLIGENCE
    COMPILE_ARGS += +define+SIGNED_INT
    
    # Optimization flags
    EXTRA_ARGS += --trace-structs
    EXTRA_ARGS += --trace-params
    EXTRA_ARGS += --trace-underscore
endif

# Test parameters - can be overridden
IA_W ?= 16
IB_W ?= 16
TH_W ?= 2

# Pass parameters to the testbench
PLUSARGS += +IA_W=$(IA_W)
PLUSARGS += +IB_W=$(IB_W)
PLUSARGS += +TH_W=$(TH_W)

# Cocotb configuration
export COCOTB_REDUCED_LOG_FMT=1
export PYTHONPATH := $(PWD):$(PYTHONPATH)

# Default target
all: test

# Run tests
test: clean
	@echo "Running cocotb tests for zero_det_neg module"
	@echo "Parameters: IA_W=$(IA_W), IB_W=$(IB_W), TH_W=$(TH_W)"
	@echo "Simulator: $(SIM)"
	$(MAKE) sim

# Test with different parameter configurations
test_configs:
	@echo "Testing different parameter configurations..."
	$(MAKE) test IA_W=8 IB_W=8 TH_W=2
	$(MAKE) test IA_W=16 IB_W=16 TH_W=2
	$(MAKE) test IA_W=32 IB_W=32 TH_W=3

# Test with different simulators (if available)
test_all_sims:
	@echo "Testing with Verilator..."
	$(MAKE) test SIM=verilator
	@if command -v vcs >/dev/null 2>&1; then \
		echo "Testing with VCS..."; \
		$(MAKE) test SIM=vcs; \
	fi
	@if command -v vsim >/dev/null 2>&1; then \
		echo "Testing with ModelSim/QuestaSim..."; \
		$(MAKE) test SIM=questa; \
	fi

# Clean build artifacts (use :: to avoid conflict with cocotb)
clean::
	@echo "Cleaning build artifacts..."
	rm -rf sim_build/
	rm -rf __pycache__/
	rm -rf .pytest_cache/
	rm -f results.xml
	rm -f *.vcd
	rm -f *.fst
	rm -f *.log

# Help target
help:
	@echo "Available targets:"
	@echo "  test          - Run tests with default parameters"
	@echo "  test_configs  - Run tests with different parameter configurations"
	@echo "  test_all_sims - Run tests with all available simulators"
	@echo "  clean         - Clean build artifacts"
	@echo "  help          - Show this help"
	@echo ""
	@echo "Parameters:"
	@echo "  IA_W          - Input A width (default: 16)"
	@echo "  IB_W          - Input B width (default: 16)"
	@echo "  TH_W          - Threshold width (default: 2)"
	@echo "  SIM           - Simulator (default: verilator)"
	@echo ""
	@echo "Examples:"
	@echo "  make test IA_W=8 IB_W=8"
	@echo "  make test SIM=questa"

# Include cocotb makefiles
include $(shell cocotb-config --makefiles)/Makefile.sim

.PHONY: all test test_configs test_all_sims clean help
