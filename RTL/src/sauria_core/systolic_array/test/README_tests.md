# Zero Detection and Negligence Logic Tests

This directory contains comprehensive cocotb-based tests for the `zero_det_neg.sv` module, which implements zero detection and negligence logic for approximate computing in the SAURIA systolic array neural network accelerator.

## Module Overview

The `zero_det_neg` module serves a critical role in energy-efficient neural network computation by:

1. **Exact Zero Detection**: Detecting when either input operand is exactly zero
2. **Negligence Logic**: Implementing approximate computing by identifying when operands are "small enough" to be considered negligible

### Mathematical Principles

The negligence logic implements a form of approximate computing based on magnitude thresholding:

- **For Signed Integers**: Values are considered negligible when their most significant bits (beyond the threshold) follow specific patterns:
  - Positive small values: upper bits are all zeros
  - Negative small values: upper bits are all ones (two's complement representation)

- **Threshold Mechanism**: A configurable threshold (`i_thres`) determines how many least significant bits to ignore when checking for negligence

- **Combined Detection**: The output is high when either exact zero detection OR negligence detection is triggered

### Energy Efficiency Benefits

In neural network accelerators, many operands are small or zero due to:
- Sparse activation patterns
- Weight pruning techniques
- Quantization effects

By detecting these cases, the systolic array can skip expensive multiply-accumulate operations, saving significant energy.

## Test Structure

### Test Files

- `test_zero_det_neg.py` - Main test suite with comprehensive test cases
- `Makefile` - Build configuration for cocotb tests
- `README_tests.md` - This documentation file

### Test Categories

1. **Exact Zero Detection Tests**
   - Verify detection when either operand is zero
   - Test with various non-zero combinations to ensure no false positives

2. **Negligence Logic Tests**
   - Test threshold-based negligence detection
   - Verify correct behavior for different threshold values (0, 1, 2, 3)
   - Test signed integer patterns for positive and negative small values

3. **Boundary Condition Tests**
   - Test maximum and minimum values
   - Test values at threshold boundaries
   - Verify edge cases don't cause incorrect behavior

4. **Asymmetric Tests**
   - Verify that BOTH operands must be negligible for detection
   - Test cases where only one operand is small

5. **Signed Number Pattern Tests**
   - Comprehensive testing of negative number negligence patterns
   - Verify two's complement representation handling

6. **Random Comprehensive Tests**
   - Large-scale random testing for robustness
   - Stress testing with various input combinations

7. **Threshold Progression Tests**
   - Verify how the same values behave with increasing thresholds
   - Ensure monotonic behavior (higher thresholds don't reduce detection)

## Running Tests

### Prerequisites

```bash
# Install cocotb and dependencies
pip install cocotb pytest numpy

# Ensure Verilator is installed and in PATH
verilator --version
```

### Basic Test Execution

```bash
# Run all tests with default parameters (IA_W=16, IB_W=16, TH_W=2)
make test

# Run tests with custom parameters
make test IA_W=8 IB_W=8 TH_W=2

# Test multiple configurations
make test_configs

# Clean build artifacts
make clean
```

### Advanced Testing

```bash
# Test with different simulators (if available)
make test SIM=verilator
make test SIM=vcs
make test SIM=questa

# Run all available simulators
make test_all_sims

# Get help
make help
```

### Test Parameters

- `IA_W`: Input A bit width (default: 16)
- `IB_W`: Input B bit width (default: 16)  
- `TH_W`: Threshold bit width (default: 2)
- `SIM`: Simulator choice (default: verilator)

## Test Results Interpretation

### Expected Behavior

1. **Threshold = 0**: Only exact zeros should trigger detection
2. **Threshold = 1**: Values with only LSB set should be negligible
3. **Threshold = 2**: Values with only 2 LSBs set should be negligible
4. **Threshold = 3**: Values with only 3 LSBs set should be negligible

### Negligence Examples (16-bit signed)

With threshold = 1:
- Negligible: 0x0001 (+1), 0xFFFF (-1)
- Not negligible: 0x0002 (+2), 0xFFFE (-2)

With threshold = 2:
- Negligible: 0x0001 (+1), 0x0003 (+3), 0xFFFF (-1), 0xFFFD (-3)
- Not negligible: 0x0004 (+4), 0xFFFC (-4)

## Integration with SAURIA

This module is instantiated in the systolic array processing element (`sa_processing_element.sv`) when zero gating is enabled. The detection output is used to:

1. Gate multiplier operations to save energy
2. Skip accumulation when operands are negligible
3. Implement approximate computing for neural network inference

## Debugging

### Common Issues

1. **False Positives**: Check threshold configuration and signed number handling
2. **False Negatives**: Verify bit masking logic and two's complement patterns
3. **Parameter Mismatches**: Ensure test parameters match module instantiation

### Debug Features

- Comprehensive logging of test cases and expected vs. actual results
- Threshold progression analysis
- Random test failure reporting with detailed information

## Contributing

When adding new tests:

1. Follow the existing test structure and naming conventions
2. Add comprehensive documentation for new test scenarios
3. Ensure tests cover both positive and negative cases
4. Include boundary condition testing
5. Update this README with new test descriptions

## References

- SAURIA Paper: "An Energy-Efficient GeMM-Based Convolution Accelerator With On-the-Fly im2col"
- SystemVerilog source: `zero_det_neg.sv`
- Processing element integration: `sa_processing_element.sv`
